import type React from 'react'
import type { Metada<PERSON> } from 'next'
import { Co<PERSON>orant_SC, Montserrat } from 'next/font/google'
import './globals.css'
import Header from '@/components/header-footer/header'
import Footer from '@/components/header-footer/footer'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from 'sonner'
import {
  generateSEOMetadata,
  generateBusinessStructuredData,
} from '@/components/seo/seo-head'
import { Analytics } from '@vercel/analytics/next'

const cormorantSC = Cormorant_SC({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-cormorant-sc',
})

const montserrat = Montserrat({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-montserrat',
})

export const metadata: Metadata = generateSEOMetadata({
  title: 'Under Construction',
  description:
    "Sagebrush Creative is currently under construction. We're working hard to bring you an incredible new website experience. Contact us about your project!",
  keywords: [
    'under construction',
    'coming soon',
    'web design',
    'web development',
  ],
  type: 'website',
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const businessStructuredData = generateBusinessStructuredData()

  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(businessStructuredData),
          }}
        />
      </head>
      <body
        className={`${cormorantSC.variable} ${montserrat.variable} min-h-screen font-montserrat`}
      >
        <ThemeProvider
          attribute='class'
          defaultTheme='light'
          enableSystem
          disableTransitionOnChange
        >
          <div className='flex flex-col min-h-screen'>
            <Header />
            <main className='flex-grow'>
              {children}
              <Analytics />
            </main>
            <Footer />
          </div>
          <Toaster position='top-center' richColors />
        </ThemeProvider>
      </body>
    </html>
  )
}
